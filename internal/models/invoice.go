package models

import "time"

type Invoice struct {
	Id                   uint             `json:"id"`
	ClientId             uint             `json:"client_id"`
	CreatedById          *uint            `json:"created_by_id"`
	CouponId             *uint            `json:"coupon_id"`
	TaxRateId            *uint            `json:"tax_rate_id"`
	PaymentTypeId        *uint            `json:"payment_type_id"`
	CurrencyId           *uint            `json:"currency_id"`
	Reference            *string          `json:"reference"`
	Date                 *time.Time       `json:"date"`
	DueDate              *time.Time       `json:"due_date"`
	Amount               *float64         `json:"amount"`
	Discount             *float64         `json:"discount"`
	DiscountType         *string          `json:"discount_type" gorm:"default:'percentage'"`
	Balance              *float64         `json:"balance"`
	Status               *string          `json:"status" gorm:"default:'draft'"`
	PaymentStatus        *string          `json:"payment_status" gorm:"default:'unpaid'"`
	Xrate                *float64         `json:"xrate" gorm:"default:1"`
	DiscountAmount       *float64         `json:"discount_amount"`
	CouponDiscountAmount *float64         `json:"coupon_discount_amount"`
	TaxAmount            *float64         `json:"tax_amount"`
	Viewed               *bool            `json:"viewed" gorm:"default:false"`
	ViewedAt             *time.Time       `json:"viewed_at"`
	AdminNotes           *string          `json:"admin_notes" gorm:"type:text"`
	Terms                *string          `json:"terms" gorm:"type:text"`
	Sent                 *bool            `json:"sent" gorm:"default:false"`
	SentAt               *time.Time       `json:"sent_at"`
	PaymentData          *string          `json:"payment_data" gorm:"type:text"`
	Recurring            *bool            `json:"recurring" gorm:"default:false"`
	RecurFrequency       *uint            `json:"recur_frequency"`
	RecurStartDate       *time.Time       `json:"recur_start_date"`
	RecurEndDate         *time.Time       `json:"recur_end_date"`
	RecurNextDate        *time.Time       `json:"recur_next_date"`
	Description          *string          `json:"description" gorm:"type:text"`
	CreatedAt            *time.Time       `json:"created_at"`
	UpdatedAt            *time.Time       `json:"updated_at"`
	InvoiceItems         []InvoiceItem    `json:"invoice_items"`
	PaymentType          PaymentType      `json:"payment_type"`
	Client               Client           `json:"client"`
	InvoicePayments      []InvoicePayment `json:"invoice_payments"`
	IsSubscription       *bool            `json:"is_subscription" gorm:"default:false"`
}
type InvoiceRequest struct {
	ClientId             uint       `json:"client_id" binding:"required"`
	CouponId             *uint      `json:"coupon_id,omitempty"`
	TaxRateId            *uint      `json:"tax_rate_id,omitempty"`
	PaymentTypeId        *uint      `json:"payment_type_id,omitempty"`
	CurrencyId           *uint      `json:"currency_id" binding:"required"`
	IsSubscription       *bool      `json:"is_subscription,omitempty"`
	Reference            *string    `json:"reference,omitempty"`
	Date                 *time.Time `json:"date" binding:"required"`
	DueDate              *time.Time `json:"due_date,omitempty"`
	Amount               *float64   `json:"amount" binding:"required"`
	Discount             *float64   `json:"discount,omitempty"`
	DiscountType         *string    `json:"discount_type,omitempty"`
	Status               *string    `json:"status,omitempty"`
	Balance              *float64   `json:"balance"`
	Xrate                *float64   `json:"xrate"`
	DiscountAmount       *float64   `json:"discount_amount,omitempty"`
	CouponDiscountAmount *float64   `json:"coupon_discount_amount,omitempty"`
	TaxAmount            *float64   `json:"tax_amount"`
	Viewed               *bool      `json:"viewed,omitempty"`
	ViewedAt             *time.Time `json:"viewed_at,omitempty"`
	AdminNotes           *string    `json:"admin_notes,omitempty"`
	Terms                *string    `json:"terms,omitempty"`
	PaymentData          *string    `json:"payment_data,omitempty"`
	Recurring            *bool      `json:"recurring,omitempty"`
	RecurFrequency       *uint      `json:"recur_frequency,omitempty"`
	RecurStartDate       *time.Time `json:"recur_start_date,omitempty"`
	RecurEndDate         *time.Time `json:"recur_end_date,omitempty"`
	RecurNextDate        *time.Time `json:"recur_next_date,omitempty"`
	Description          *string    `json:"description,omitempty"`
	InvoiceItems         []InvoiceItemRequest
}
