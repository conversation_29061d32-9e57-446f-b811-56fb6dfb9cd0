package models

import "time"

type User struct {
	Id                     uint       `json:"id" gorm:"primaryKey"`
	CreatedById            uint       `json:"created_by_id"`
	RoleId                 uint       `json:"role_id"`
	Role                   Role       `json:"role"`
	Name                   string     `json:"name"`
	Email                  string     `json:"email" gorm:"unique"`
	Username               *string    `json:"username" gorm:"unique"`
	EmailVerifiedAt        *string    `json:"email_verified_at"`
	Password               string     `json:"password"`
	UserType               *string    `json:"user_type" gorm:"default:'backend'"`
	TwoFactorSecret        *string    `json:"two_factor_secret" gorm:"type:text"`
	TwoFactorRecoveryCodes *string    `json:"two_factor_recovery_codes" gorm:"type:text"`
	RememberToken          *string    `json:"remember_token"`
	LastLoginDate          *time.Time `json:"last_login_date"`
	Gender                 *string    `json:"gender"`
	TelegramUserId         *string    `json:"telegram_user_id"`
	SlackWebhookUrl        *string    `json:"slack_webhook_url"`
	Status                 *string    `json:"status"`
	Token                  *string    `json:"token"`
	Description            *string    `json:"description" gorm:"type:text"`
	CreatedAt              time.Time  `json:"created_at"`
	UpdatedAt              time.Time  `json:"updated_at"`
}
type CreateUserRequest struct {
	Name            string  `json:"name" binding:"required"`
	Email           string  `json:"email" binding:"required,email"`
	Password        string  `json:"password" binding:"required,min=6"`
	RoleId          uint    `json:"role_id" binding:"required"`
	Username        *string `json:"username,omitempty"`
	Gender          *string `json:"gender,omitempty"`
	TelegramUserId  *string `json:"telegramUserId,omitempty"`
	SlackWebhookUrl *string `json:"slack_webhook_url,omitempty"`
	Status          *string `json:"status" binding:"required"`
	Description     *string `json:"description,omitempty"`
}
type UpdateUserRequest struct {
	Name            string  `json:"name" binding:"required"`
	Email           string  `json:"email" binding:"required,email"`
	RoleId          *uint   `json:"role_id" binding:"required"`
	Password        string  `json:"password,omitempty"`
	Username        *string `json:"username,omitempty"`
	Gender          *string `json:"gender,omitempty"`
	TelegramUserId  *string `json:"telegram_user_id,omitempty"`
	SlackWebhookUrl *string `json:"slack_webhook_url,omitempty"`
	Status          *string `json:"status" binding:"required"`
	Description     *string `json:"description,omitempty"`
}
