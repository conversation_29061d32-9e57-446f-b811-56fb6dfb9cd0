package seed

import (
	"log"
	"os"
	"strings"
	"yotracker/config"
)

func runSqlFile(filePath string) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		log.Fatalf("Failed to read file:%s,%v", filePath, err)
	}
	queries := strings.Split(string(content), ";")
	for _, query := range queries {
		if strings.TrimSpace(query) != "" {
			err := config.DB.Exec(query).Error
			if err != nil {
				log.Fatalf("Failed to execute query:%s,%v", query, err)
			}
		}
	}
	config.DB.Exec(string(content))
}
func Seed() {
	config.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	runSqlFile("migrations/seeders/settings.sql")
	runSqlFile("migrations/seeders/countries.sql")
	runSqlFile("migrations/seeders/protocols.sql")
	runSqlFile("migrations/seeders/device_types.sql")
	runSqlFile("migrations/seeders/roles.sql")
	//runSqlFile("migrations/seeders/permissions.sql")
	//runSqlFile("migrations/seeders/role_permissions.sql")
	runSqlFile("migrations/seeders/currencies.sql")
	runSqlFile("migrations/seeders/payment_types.sql")
	runSqlFile("migrations/seeders/settings.sql")
	runSqlFile("migrations/seeders/users.sql")
	config.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")
}
