package utils

import (
	"testing"
	"yotracker/config"
)

func TestGenerateReference(t *testing.T) {
	// Initialize test database
	config.InitTestDB()

	// Test cases
	tests := []struct {
		name        string
		id          string
		settingKey  string
		expectStart string
		expectLen   int
	}{
		{
			name:        "Single digit ID with default setting",
			id:          "1",
			settingKey:  "",
			expectStart: "INV-001",
			expectLen:   7,
		},
		{
			name:        "Two digit ID",
			id:          "12",
			settingKey:  "invoice_reference_prefix",
			expectStart: "INV-012",
			expectLen:   7,
		},
		{
			name:        "Three digit ID",
			id:          "123",
			settingKey:  "invoice_reference_prefix",
			expectStart: "INV-123",
			expectLen:   7,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GenerateReference(tt.id, tt.settingKey)

			if len(result) != tt.expectLen {
				t.<PERSON>("GenerateReference() length = %v, want %v", len(result), tt.expectLen)
			}

			if result != tt.expectStart {
				t.<PERSON>rf("GenerateReference() = %v, want %v", result, tt.expectStart)
			}
		})
	}
}

func TestGenerateReferenceWithYearFormat(t *testing.T) {
	// Initialize test database
	config.InitTestDB()

	result := GenerateReference("1", "invoice_reference_prefix")

	// Should contain current year
	if len(result) < 10 { // INV-2024/001 = 11 chars minimum
		t.Errorf("GenerateReference() with year format too short: %v", result)
	}

	// Should start with prefix
	if result[:4] != "INV-" {
		t.Errorf("GenerateReference() should start with INV-, got: %v", result)
	}

	// Should contain a slash
	found := false
	for _, char := range result {
		if char == '/' {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("GenerateReference() with year format should contain '/', got: %v", result)
	}
}

func TestGenerateUniqueID(t *testing.T) {
	id1 := generateUniqueID()
	id2 := generateUniqueID()

	if id1 == id2 {
		t.Errorf("generateUniqueID() should generate unique IDs, got same: %v", id1)
	}

	if len(id1) != 16 { // 8 bytes = 16 hex characters
		t.Errorf("generateUniqueID() should return 16 characters, got %v: %v", len(id1), id1)
	}
}
