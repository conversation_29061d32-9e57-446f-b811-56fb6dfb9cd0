package config

import (
	"fmt"
	"github.com/joho/godotenv"
	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"yotracker/internal/models"
)

var DB *gorm.DB

const projectDirName = "yotracker"

func InitDB() {
	// Get the absolute path to the .env file
	rootDir, _ := filepath.Abs(filepath.Join(filepath.Dir("."), ".."))
	envPath := filepath.Join(rootDir+"/"+projectDirName, ".env")
	err := godotenv.Load(envPath)

	if err != nil {
		log.Fatalf("Error loading .env file")
	}
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local", os.<PERSON>env("DB_USERNAME"), os.<PERSON>("DB_PASSWORD"), os.<PERSON>en<PERSON>("DB_HOST"), os.Getenv("DB_PORT"), os.<PERSON>env("DB_NAME"))
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		fmt.Println("Failed to connect to database")
		log.Fatal("Database connection error:", err)
	}
	err = DB.AutoMigrate(&models.Alert{}, &models.Client{}, &models.ClientDevice{}, &models.CommandLog{}, &models.DeviceType{}, &models.Fleet{}, &models.GPSData{}, &models.Protocol{}, &models.User{}, &models.Role{}, &models.Permission{}, &models.RolePermission{}, &models.ClientRole{}, &models.Invoice{}, &models.InvoiceItem{}, &models.InvoicePayment{}, &models.PaymentType{}, &models.Currency{}, &models.Country{}, &models.Setting{})
	if err != nil {
		fmt.Println("Failed to migrate tables")
		log.Fatal("Migration error:", err)
	}
}
func InitTestDB() {
	var err error
	DB, err = gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{})
	if err != nil {
		fmt.Println("Failed to connect to test database")
		log.Fatal("Test database connection error:", err)
	}

	// Migrate the test database schema
	err = DB.AutoMigrate(
		&models.Alert{}, &models.Client{}, &models.ClientDevice{}, &models.CommandLog{},
		&models.DeviceType{}, &models.Fleet{}, &models.GPSData{}, &models.Protocol{},
		&models.User{}, &models.Role{}, &models.Permission{}, &models.RolePermission{},
		&models.ClientRole{}, &models.Invoice{}, &models.InvoiceItem{},
		&models.InvoicePayment{}, &models.PaymentType{}, &models.Currency{}, &models.Country{},
		&models.Setting{},
	)
	if err != nil {
		fmt.Println("Failed to migrate test tables")
		log.Fatal("Test migration error:", err)
	}
}
func loadEnv() {
	projectName := regexp.MustCompile(`^(.*` + projectDirName + `)`)
	currentWorkDirectory, _ := os.Getwd()
	rootPath := projectName.Find([]byte(currentWorkDirectory))

	err := godotenv.Load(string(rootPath) + `/.env`)

	if err != nil {
		log.Fatalf("Error loading .env file")
	}
}
func GetDB() *gorm.DB {
	return DB
}
