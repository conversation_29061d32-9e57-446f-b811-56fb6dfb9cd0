package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

func GetAllInvoices(c *gin.Context) {
	var invoices []models.Invoice
	var total int64
	filter := map[string]interface{}{}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		filter["payment_type_id"] = paymentTypeId
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		filter["currency_id"] = currencyId
	}

	config.DB.Scopes(utils.Paginate(c)).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Where(filter).Order("id desc").Find(&invoices)
	config.DB.Model(&models.Invoice{}).Where(filter).Count(&total)
	c.JSON(http.StatusOK, gin.H{
		"data":         invoices,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}

func GetInvoiceById(c *gin.Context) {
	var invoice models.Invoice
	if err := config.DB.Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": invoice,
	})
}

func CreateInvoice(c *gin.Context) {
	// Define a custom request struct to handle invoice items
	type InvoiceWithItemsRequest struct {
		models.InvoiceRequest
		InvoiceItems []models.InvoiceItemRequest `json:"items"`
	}

	var req InvoiceWithItemsRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	userIDValue, _ := c.Get("userID")
	userID := userIDValue.(uint)
	invoice := models.Invoice{
		ClientId:             req.ClientId,
		CreatedById:          &userID,
		CouponId:             req.CouponId,
		TaxRateId:            req.TaxRateId,
		PaymentTypeId:        req.PaymentTypeId,
		CurrencyId:           req.CurrencyId,
		Date:                 req.Date,
		DueDate:              req.DueDate,
		Amount:               req.Amount,
		Discount:             req.Discount,
		DiscountType:         req.DiscountType,
		Status:               req.Status,
		Balance:              req.Balance,
		Xrate:                req.Xrate,
		DiscountAmount:       req.DiscountAmount,
		CouponDiscountAmount: req.CouponDiscountAmount,
		TaxAmount:            req.TaxAmount,
		AdminNotes:           req.AdminNotes,
		Terms:                req.Terms,
		Recurring:            req.Recurring,
		RecurFrequency:       req.RecurFrequency,
		RecurStartDate:       req.RecurStartDate,
		RecurEndDate:         req.RecurEndDate,
		RecurNextDate:        req.RecurNextDate,
		Description:          req.Description,
		IsSubscription:       req.IsSubscription,
	}

	// Use a transaction to ensure both invoice and items are created together
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Create(&invoice).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Create invoice items if provided
	if len(req.InvoiceItems) > 0 {
		for i := range req.InvoiceItems {
			// Set the invoice ID for each item

			// Create the invoice item
			invoiceItem := models.InvoiceItem{
				InvoiceId:            invoice.Id,
				ClientDeviceId:       req.InvoiceItems[i].ClientDeviceId,
				TaxRateId:            req.InvoiceItems[i].TaxRateId,
				Name:                 req.InvoiceItems[i].Name,
				Description:          req.InvoiceItems[i].Description,
				Quantity:             req.InvoiceItems[i].Quantity,
				ItemPosition:         req.InvoiceItems[i].ItemPosition,
				UnitCost:             req.InvoiceItems[i].UnitCost,
				BaseCurrencyUnitCost: req.InvoiceItems[i].BaseCurrencyUnitCost,
				Discount:             req.InvoiceItems[i].Discount,
				DiscountType:         req.InvoiceItems[i].DiscountType,
				DiscountAmount:       req.InvoiceItems[i].DiscountAmount,
				TaxAmount:            req.InvoiceItems[i].TaxAmount,
				Total:                req.InvoiceItems[i].Total,
			}

			if err := tx.Create(&invoiceItem).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"message": "Failed to create invoice item: " + err.Error(),
				})
				return
			}
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to commit transaction: " + err.Error(),
		})
		return
	}

	// Fetch the complete invoice with items to return in the response
	var completeInvoice models.Invoice
	config.DB.Preload("InvoiceItems").First(&completeInvoice, invoice.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice created successfully",
		"data":    completeInvoice,
	})
}

func UpdateInvoice(c *gin.Context) {
	// Define a custom request struct to handle invoice items
	type InvoiceWithItemsRequest struct {
		models.InvoiceRequest
		InvoiceItems []models.InvoiceItemRequest `json:"invoice_items"`
	}

	var req InvoiceWithItemsRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	var invoice models.Invoice
	if err := config.DB.First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}

	invoice.ClientId = req.ClientId
	invoice.CouponId = req.CouponId
	invoice.TaxRateId = req.TaxRateId
	invoice.PaymentTypeId = req.PaymentTypeId
	invoice.CurrencyId = req.CurrencyId
	invoice.Reference = req.Reference
	invoice.Date = req.Date
	invoice.DueDate = req.DueDate
	invoice.Amount = req.Amount
	invoice.Discount = req.Discount
	invoice.DiscountType = req.DiscountType
	invoice.Status = req.Status
	invoice.Balance = req.Balance
	invoice.Xrate = req.Xrate
	invoice.DiscountAmount = req.DiscountAmount
	invoice.CouponDiscountAmount = req.CouponDiscountAmount
	invoice.TaxAmount = req.TaxAmount
	invoice.AdminNotes = req.AdminNotes
	invoice.Terms = req.Terms
	invoice.PaymentData = req.PaymentData
	invoice.Recurring = req.Recurring
	invoice.RecurFrequency = req.RecurFrequency
	invoice.RecurStartDate = req.RecurStartDate
	invoice.RecurEndDate = req.RecurEndDate
	invoice.RecurNextDate = req.RecurNextDate
	invoice.Description = req.Description
	invoice.IsSubscription = req.IsSubscription

	// Use a transaction to ensure both invoice and items are updated together
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Save(&invoice).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Handle invoice items if provided
	if len(req.InvoiceItems) > 0 {
		// Delete existing invoice items
		if err := tx.Where("invoice_id = ?", invoice.Id).Delete(&models.InvoiceItem{}).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"message": "Failed to delete existing invoice items: " + err.Error(),
			})
			return
		}

		// Create new invoice items
		for i := range req.InvoiceItems {
			// Set the invoice ID for each item

			// Create the invoice item
			invoiceItem := models.InvoiceItem{
				InvoiceId:            invoice.Id,
				ClientDeviceId:       req.InvoiceItems[i].ClientDeviceId,
				TaxRateId:            req.InvoiceItems[i].TaxRateId,
				Name:                 req.InvoiceItems[i].Name,
				Description:          req.InvoiceItems[i].Description,
				Quantity:             req.InvoiceItems[i].Quantity,
				ItemPosition:         req.InvoiceItems[i].ItemPosition,
				UnitCost:             req.InvoiceItems[i].UnitCost,
				BaseCurrencyUnitCost: req.InvoiceItems[i].BaseCurrencyUnitCost,
				Discount:             req.InvoiceItems[i].Discount,
				DiscountType:         req.InvoiceItems[i].DiscountType,
				DiscountAmount:       req.InvoiceItems[i].DiscountAmount,
				TaxAmount:            req.InvoiceItems[i].TaxAmount,
				Total:                req.InvoiceItems[i].Total,
			}

			if err := tx.Create(&invoiceItem).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"message": "Failed to create invoice item: " + err.Error(),
				})
				return
			}
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to commit transaction: " + err.Error(),
		})
		return
	}

	// Fetch the complete invoice with items to return in the response
	var completeInvoice models.Invoice
	config.DB.Preload("InvoiceItems").First(&completeInvoice, invoice.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice updated successfully",
		"data":    completeInvoice,
	})
}

func DeleteInvoice(c *gin.Context) {
	var invoice models.Invoice
	if err := config.DB.First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}

	result := config.DB.Delete(&invoice)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	//delete invoice items
	config.DB.Where("invoice_id = ?", invoice.Id).Delete(&models.InvoiceItem{})
	//delete invoice payments
	config.DB.Where("invoice_id = ?", invoice.Id).Delete(&models.InvoicePayment{})
	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice deleted successfully",
	})
}

func SearchInvoices(c *gin.Context) {
	var invoices []models.Invoice
	filter := map[string]interface{}{}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}

	if reference := c.Query("reference"); reference != "" {
		config.DB.Where(filter).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Where("reference LIKE ?", "%"+reference+"%").Order("id desc").Find(&invoices)
	} else {
		config.DB.Where(filter).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Order("id desc").Find(&invoices)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": invoices,
	})
}
