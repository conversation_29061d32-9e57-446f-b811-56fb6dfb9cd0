package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/response"
	"yotracker/internal/service"
	"yotracker/internal/utils"
)

func GetAllClients(c *gin.Context) {
	var clients []models.Client
	var total int64
	filter := map[string]interface{}{}
	if clientType := c.Query("client_type"); clientType != "" {
		filter["client_type"] = clientType
	}
	if staffId := c.Query("staff_id"); staffId != "" {
		filter["staff_id"] = staffId
	}
	config.DB.Scopes(utils.Paginate(c)).Where(filter).Order("id desc").Find(&clients)
	config.DB.Model(&models.Client{}).Where(filter).Count(&total)

	var clientResponses []response.ClientResponse
	for _, client := range clients {
		clientResponse := response.ClientResponse{
			Id:              client.Id,
			Name:            client.Name,
			Email:           client.Email,
			ClientType:      client.ClientType,
			Status:          client.Status,
			CreatedById:     client.CreatedById,
			PhoneNumber:     client.PhoneNumber,
			ReferredById:    client.ReferredById,
			BranchId:        client.BranchId,
			StaffId:         client.StaffId,
			CountryId:       client.CountryId,
			Gender:          client.Gender,
			Company:         client.Company,
			State:           client.State,
			City:            client.City,
			Town:            client.Town,
			Address:         client.Address,
			Description:     client.Description,
			BillingCycle:    client.BillingCycle,
			BillingDay:      client.BillingDay,
			IsLifetime:      client.IsLifetime,
			NextBillingDate: utils.FormatTimeOrEmptyString(client.NextBillingDate),
			LastBilledAt:    utils.FormatTimeOrEmptyString(client.LastBilledAt),
			SuspendedAt:     utils.FormatTimeOrEmptyString(client.SuspendedAt),
			CreatedAt:       utils.FormatTimeOrEmptyString(&client.CreatedAt),
			UpdatedAt:       utils.FormatTimeOrEmptyString(&client.UpdatedAt),
		}
		clientResponses = append(clientResponses, clientResponse)
	}
	c.JSON(http.StatusOK, gin.H{
		"data":         clientResponses,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}
func GetClientById(c *gin.Context) {
	var client models.Client
	if err := config.DB.Preload("Country").First(&client, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client not found",
		})
		return
	}
	clientResponse := response.ClientResponse{
		Id:              client.Id,
		Name:            client.Name,
		Email:           client.Email,
		ClientType:      client.ClientType,
		Status:          client.Status,
		CreatedById:     client.CreatedById,
		PhoneNumber:     client.PhoneNumber,
		ReferredById:    client.ReferredById,
		BranchId:        client.BranchId,
		StaffId:         client.StaffId,
		CountryId:       client.CountryId,
		Country:         client.Country,
		Gender:          client.Gender,
		Company:         client.Company,
		State:           client.State,
		City:            client.City,
		Town:            client.Town,
		Address:         client.Address,
		Description:     client.Description,
		BillingCycle:    client.BillingCycle,
		BillingDay:      client.BillingDay,
		IsLifetime:      client.IsLifetime,
		NextBillingDate: utils.FormatTimeOrEmptyString(client.NextBillingDate),
		LastBilledAt:    utils.FormatTimeOrEmptyString(client.LastBilledAt),
		SuspendedAt:     utils.FormatTimeOrEmptyString(client.SuspendedAt),
		CreatedAt:       utils.FormatTimeOrEmptyString(&client.CreatedAt),
		UpdatedAt:       utils.FormatTimeOrEmptyString(&client.UpdatedAt),
	}
	c.JSON(http.StatusOK, gin.H{
		"data": clientResponse,
	})
}
func CreateClient(c *gin.Context) {
	var req models.ClientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	userIDValue, _ := c.Get("userID")
	userID := userIDValue.(uint)
	client := models.Client{
		Name:        req.Name,
		Email:       req.Email,
		ClientType:  req.ClientType,
		Status:      req.Status,
		CreatedById: userID,
		PhoneNumber: req.PhoneNumber,
	}
	if req.ReferredById != 0 {
		client.ReferredById = &req.ReferredById
	}
	if req.BranchId != 0 {
		client.BranchId = &req.BranchId
	}
	if req.StaffId != 0 {
		client.StaffId = &req.StaffId
	}
	if req.CountryId != 0 {
		client.CountryId = &req.CountryId
	}
	if req.Gender != "" {
		client.Gender = &req.Gender
	}
	if req.State != "" {
		client.State = &req.State
	}
	if req.City != "" {
		client.City = &req.City
	}
	if req.Town != "" {
		client.Town = &req.Town
	}
	if req.Address != "" {
		client.Address = &req.Address
	}
	if req.Description != "" {
		client.Description = &req.Description
	}
	if req.BillingCycle != "" {
		client.BillingCycle = &req.BillingCycle
	}
	if req.BillingDay != 0 {
		client.BillingDay = &req.BillingDay
	}
	if req.IsLifetime {
		client.IsLifetime = &req.IsLifetime
	}
	if req.NextBillingDate != nil {
		client.NextBillingDate = req.NextBillingDate
	}

	result := config.DB.Create(&client)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Client created successfully",
	})
}
func UpdateClient(c *gin.Context) {
	var req models.ClientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var client models.Client
	if err := config.DB.First(&client, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client not found",
		})
		return
	}
	client.Name = req.Name
	client.Email = req.Email
	client.ClientType = req.ClientType
	client.Status = req.Status
	client.PhoneNumber = req.PhoneNumber
	if req.ReferredById != 0 {
		client.ReferredById = &req.ReferredById
	}
	if req.BranchId != 0 {
		client.BranchId = &req.BranchId
	}
	if req.StaffId != 0 {
		client.StaffId = &req.StaffId
	}
	if req.CountryId != 0 {
		client.CountryId = &req.CountryId
	}
	if req.Gender != "" {
		client.Gender = &req.Gender
	}
	if req.State != "" {
		client.State = &req.State
	}
	if req.City != "" {
		client.City = &req.City
	}
	if req.Town != "" {
		client.Town = &req.Town
	}
	if req.Address != "" {
		client.Address = &req.Address
	}
	if req.Description != "" {
		client.Description = &req.Description
	}
	if req.BillingCycle != "" {
		client.BillingCycle = &req.BillingCycle
	}
	if req.BillingDay != 0 {
		client.BillingDay = &req.BillingDay
	}
	if req.IsLifetime {
		client.IsLifetime = &req.IsLifetime
	}
	if req.NextBillingDate != nil {
		client.NextBillingDate = req.NextBillingDate
	}
	result := config.DB.Save(&client)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Client updated successfully"})
}
func DeleteClient(c *gin.Context) {
	var client models.Client
	if err := config.DB.First(&client, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client not found",
		})
		return
	}
	result := config.DB.Delete(&client)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Client deleted successfully"})

}
func SearchClients(c *gin.Context) {
	var clients []models.Client
	config.DB.Find(&clients, "(name like ? or id like ? or email like ? or phone_number like ?)", "%"+c.Query("s")+"%", "%"+c.Query("s")+"%", "%"+c.Query("s")+"%", "%"+c.Query("s")+"%")
	var clientResponses []response.ClientResponse
	for _, client := range clients {
		clientResponse := response.ClientResponse{
			Id:           client.Id,
			Name:         client.Name,
			Email:        client.Email,
			ClientType:   client.ClientType,
			Status:       client.Status,
			CreatedById:  client.CreatedById,
			PhoneNumber:  client.PhoneNumber,
			ReferredById: client.ReferredById,
			BranchId:     client.BranchId,
			StaffId:      client.StaffId,
			CountryId:    client.CountryId,
			Gender:       client.Gender,
			Company:      client.Company,
			State:        client.State,
			City:         client.City,
			Town:         client.Town,
			Address:      client.Address,
			Description:  client.Description,
			CreatedAt:    utils.FormatTimeOrEmptyString(&client.CreatedAt),
			UpdatedAt:    utils.FormatTimeOrEmptyString(&client.UpdatedAt),
		}
		clientResponses = append(clientResponses, clientResponse)
	}
	c.JSON(http.StatusOK, gin.H{
		"data": clientResponses,
	})
}
func CreateClientUser(c *gin.Context) {
	var req models.CreateClientUserRequest
	clientId, _ := strconv.Atoi(c.Param("id"))
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	if req.ExistingUser {
		var user models.User
		userId, _ := strconv.Atoi(c.Param("id"))
		if err := config.DB.First(&user, req.UserId).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"message": "User not found",
			})
			return
		}
		//check if we don't have a client user already
		var clientUser models.ClientUser
		if err := config.DB.Where("client_id = ? AND user_id = ?", clientId, userId).First(&clientUser).Error; err == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "User already added",
			})
			return
		}

		clientUser = models.ClientUser{
			ClientId: uint(clientId),
			UserId:   uint(userId),
		}
		result := config.DB.Create(&clientUser)
		if result.Error != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": result.Error.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"message": "User added successfully",
		})
		return
	}
	if req.Password == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Password is required",
		})
		return
	}
	if req.Email == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Email is required",
		})
		return
	}
	if req.Name == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Name is required",
		})
		return
	}

	hashedPassword := service.HashPassword(*req.Password)
	status := "active"
	userType := "client"
	emailVerifiedAt := time.Now().Format(time.RFC3339)
	user := models.User{
		Name:            *req.Name,
		Email:           *req.Email,
		Password:        hashedPassword,
		Status:          &status,
		Gender:          req.Gender,
		TelegramUserId:  req.TelegramUserId,
		SlackWebhookUrl: req.SlackWebhookUrl,
		Description:     req.Description,
		UserType:        &userType,
		Username:        req.Username,
		EmailVerifiedAt: &emailVerifiedAt,
	}
	result := config.DB.Create(&user)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	clientUser := models.ClientUser{
		ClientId: uint(clientId),
		UserId:   user.Id,
	}
	result = config.DB.Create(&clientUser)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "User created successfully",
	})
}
func DeleteClientUser(c *gin.Context) {
	id := c.Param("id")
	var clientUser models.ClientUser
	if err := config.DB.First(&clientUser, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client user not found",
		})
		return
	}
	result := config.DB.Delete(&clientUser)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Client user deleted successfully"})
}
func UpdateClientUser(c *gin.Context) {

}
